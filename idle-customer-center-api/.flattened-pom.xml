<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.qudian.idle</groupId>
  <artifactId>idle-customer-center-api</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <description>Parent pom providing dependency and plugin management for applications built with <PERSON><PERSON></description>
  <url>https://spring.io/projects/spring-boot/pdt-matrix/matrix-bom/idle-customer-center/idle-customer-center-api</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Spring</name>
      <email><EMAIL></email>
      <organization>VMware, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/spring-projects/spring-boot/pdt-matrix/matrix-bom/idle-customer-center/idle-customer-center-api</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.qudian.lme</groupId>
      <artifactId>pdt-toolkit-api</artifactId>
      <version>3.0.6-jdk17-RELEASE</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.swagger.core.v3</groupId>
      <artifactId>swagger-core-jakarta</artifactId>
      <version>2.2.22</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.2.7</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>bom</flattenMode>
              <pomElements>
                <pluginManagement>keep</pluginManagement>
                <profiles>keep</profiles>
                <properties>keep</properties>
                <distributionManagement>keep</distributionManagement>
                <build>keep</build>
                <repositories>keep</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten-clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
