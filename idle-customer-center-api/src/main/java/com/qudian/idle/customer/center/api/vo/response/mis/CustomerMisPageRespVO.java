package com.qudian.idle.customer.center.api.vo.response.mis;

import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMisPageRespVO extends BaseResponseVO {
    private Long id;
    private String name;
    private String mobile;
    private Boolean hasWechat; //客户微信号
    private String source; //来源
    private String membershipType; //会员类型: 1(普通), 2（VIP会员）
    private String outputIntention; //客户出售意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    private String purchaseIntention; //客户购买意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    private Integer collectCnt;  //录入藏品数
    private Long managerId;  //跟进人ID
    private String managerName;  //跟进人名称
    private String createdTime;  //录入时间
}
