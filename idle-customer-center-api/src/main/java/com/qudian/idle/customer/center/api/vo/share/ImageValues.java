package com.qudian.idle.customer.center.api.vo.share;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.share.OptionValues</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public class ImageValues extends ArrayList<ImageValues.ImageRecord> {

    @Data
    @AllArgsConstructor
    public static class ImageRecord implements Serializable {
        private String key;
        private String url;
    }

    public ImageValues() {
        super();
    }

    public ImageValues(List<ImageRecord> list) {
        super(null != list ? list : Collections.emptyList());
    }

    public String key() {
        if (this.isEmpty()) {
            return null;
        }
        Object firstElement = this.get(0);
        if (firstElement instanceof ImageRecord opRecord) {
            return opRecord.getKey();
        } else if (firstElement instanceof JSONObject jsonObj) {
            return jsonObj.getString("key");
        }
        return null;
    }

    public String url() {
        if (this.isEmpty()) {
            return null;
        }
        Object firstElement = this.get(0);
        if (firstElement instanceof ImageRecord opRecord) {
            return opRecord.getUrl();
        } else if (firstElement instanceof JSONObject jsonObj) {
            return jsonObj.getString("url");
        }
        return null;
    }

    public static ImageValues of(String key, String url) {
        return new ImageValues(List.of(new ImageRecord(key, url)));
    }

    /**
     * 严格校验单值
     *
     * @return {@link Integer }
     */
    public String requireSingleKey() {
        if (this.isEmpty()) {
            return null;
        }
        if (this.size() > 1) {
            throw new IllegalStateException("Expect single code but got " + this.size() + " records");
        }
        Object firstElement = this.get(0);
        if (firstElement instanceof ImageRecord opRecord) {
            return opRecord.getKey();
        } else if (firstElement instanceof JSONObject jsonObj) {
            return jsonObj.getString("key");
        } else {
            throw new IllegalStateException("Expected OptionRecord but got " + firstElement.getClass().getName() + ": " + firstElement);
        }
    }

    public List<String> keys() {
        List<String> list = new ArrayList<>(this.size());
        for (Object obj : this) {
            ImageRecord r = null;
            if (obj instanceof ImageRecord opRecord) {
                r = opRecord;
            } else if (obj instanceof JSONObject jsonObj) {
                String code = jsonObj.getString("key");
                if (code != null) {
                    list.add(code);
                }
                continue;
            } else {
                // Skip non-OptionRecord objects
                continue;
            }
            if (null == r || null == r.getKey()) {
                continue;
            }
            list.add(r.getKey());
        }
        return list;
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }

    public static ImageValues fromJson(String json) {
        List<ImageRecord> list = JSON.parseArray(json, ImageRecord.class);
        return new ImageValues(list);
    }
}
