package com.qudian.idle.customer.center.api.vo.share;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.api.vo.share.OptionValues</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public class OptionValues extends ArrayList<OptionValues.OptionRecord> {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OptionRecord implements Serializable {
        private static final long serialVersionUID = -101678720028544738L;
        private String code;
        private String title;
    }

    public OptionValues() {
        super();
    }

    public OptionValues(List<OptionRecord> list) {
        super(null != list ? list : Collections.emptyList());
    }

    public String code() {
        if (this.isEmpty()) {
            return null;
        }
        Object firstElement = this.get(0);
        if (firstElement instanceof OptionRecord opRecord) {
            return opRecord.getCode();
        } else if (firstElement instanceof JSONObject jsonObj) {
            return jsonObj.getString("code");
        }
        return null;
    }

    public String title() {
        if (this.isEmpty()) {
            return null;
        }
        Object firstElement = this.get(0);
        if (firstElement instanceof OptionRecord opRecord) {
            return opRecord.getTitle();
        } else if (firstElement instanceof JSONObject jsonObj) {
            return jsonObj.getString("title");
        }
        return null;
    }

    public static OptionValues of(String code, String title) {
        return new OptionValues(List.of(new OptionRecord(code, title)));
    }

    /**
     * 严格校验单值
     *
     * @return {@link Integer }
     */
    public String requireSingleCode() {
        if (this.isEmpty()) {
            return null;
        }
        if (this.size() > 1) {
            throw new IllegalStateException("Expect single code but got " + this.size() + " records");
        }
        Object firstElement = this.get(0);
        if (firstElement instanceof OptionRecord opRecord) {
            return opRecord.getCode();
        } else if (firstElement instanceof JSONObject jsonObj) {
            return jsonObj.getString("code");
        } else {
            throw new IllegalStateException("Expected OptionRecord but got " + firstElement.getClass().getName() + ": " + firstElement);
        }
    }

    public List<String> codes() {
        List<String> list = new ArrayList<>(this.size());
        for (Object obj : this) {
            OptionRecord r = null;
            if (obj instanceof OptionRecord opRecord) {
                r = opRecord;
            } else if (obj instanceof JSONObject jsonObj) {
                String code = jsonObj.getString("code");
                if (code != null) {
                    list.add(code);
                }
                continue;
            } else {
                // Skip non-OptionRecord objects
                continue;
            }
            if (null == r || null == r.getCode()) {
                continue;
            }
            list.add(r.getCode());
        }
        return list;
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }

    public static OptionValues fromJson(String json) {
        List<OptionRecord> list = JSON.parseArray(json, OptionRecord.class);
        return new OptionValues(list);
    }
}
