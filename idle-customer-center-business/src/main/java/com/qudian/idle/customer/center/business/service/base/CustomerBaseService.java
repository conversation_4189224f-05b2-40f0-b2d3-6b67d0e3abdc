package com.qudian.idle.customer.center.business.service.base;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBaseInsideReqVO;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHomeReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisPageReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisTransferReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerBaseInsideRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import com.qudian.pdt.api.toolkit.vo.response.PagingList;

import javax.sound.midi.VoiceStatus;
import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.business.service.base.CustomerBaseService</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
public interface CustomerBaseService {

    /**
     * 添加/编辑客户基础信息
     *
     * @param editReqVO
     * @return {@link IdRequestVO }
     */
    IdRequestVO createOrUpdate(CustomerEditReqVO editReqVO);

    /**
     * 查询客户基础信息
     *
     * @param showReqVO
     * @return {@link CustomerShowRespVO }
     */
    CustomerShowRespVO query(CustomerShowReqVO showReqVO);

    /**
     * 批量查询客户信息（首页）
     *
     * @param reqVO
     * @return {@link List }<{@link CustomerInfoInHomeRespVO }>
     */
    List<CustomerInfoInHomeRespVO> batchQueryCustomerInfoInHome(CustomerBatchQueryInHomeReqVO reqVO);

    /**
     * MIS端客户列表
     *
     * @param reqVO
     * @return {@link PagingList }<{@link CustomerMisPageRespVO }>
     */
    PagingList<CustomerMisPageRespVO> page(CustomerMisPageReqVO reqVO);

    /**
     * MIS端客户关系流转
     *
     * @param reqVO
     */
    void transfer(CustomerMisTransferReqVO reqVO);

    /**
     * 查询客户基础信息
     *
     * @param reqVO
     * @return {@link CustomerBaseInsideRespVO }
     */
    CustomerBaseInsideRespVO queryCustomerBaseInfo(CustomerBaseInsideReqVO reqVO);
}
