package com.qudian.idle.customer.center.business.service.base.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.qudian.idle.customer.center.api.enums.CustomerExtTypeEnum;
import com.qudian.idle.customer.center.api.enums.CustomerPreferBrandEnum;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBaseInsideReqVO;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHomeReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisPageReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisTransferReqVO;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerBaseInsideRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.api.vo.share.IdRequestVO;
import com.qudian.idle.customer.center.api.vo.share.ImageValues;
import com.qudian.idle.customer.center.api.vo.share.OptionValues;
import com.qudian.idle.customer.center.business.service.base.CustomerBaseService;
import com.qudian.idle.customer.center.business.service.base.CustomerGroupService;
import com.qudian.idle.customer.center.business.service.base.CustomerPropertyService;
import com.qudian.idle.customer.center.common.utils.common.StringUtil;
import com.qudian.idle.customer.center.infrastructure.assembler.CustomerBaseStruct;
import com.qudian.idle.customer.center.infrastructure.assembler.CustomerInsideStruct;
import com.qudian.idle.customer.center.infrastructure.converter.ContextUtil;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerBaseRepository;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerExtRepository;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerGroupRelationRepository;
import com.qudian.idle.customer.center.infrastructure.repository.CustomerManagerLogRepository;
import com.qudian.idle.customer.center.infrastructure.repository.database.mapper.CustomerGroupMapper;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerExtPO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerManagerLogPO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupPO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.group.CustomerGroupRelationPO;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.dto.crm.CrmGoodsCntDTO;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.CrmGoodsRemote;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.ToolSrvRemote;
import com.qudian.pdt.api.toolkit.vo.response.PagingList;
import com.qudian.pdt.toolkit.common.exception.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.customer.center.business.service.base.impl.CustomerBaseServiceImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Slf4j
@Service
public class CustomerBaseServiceImpl implements CustomerBaseService {
    @Resource
    private CustomerBaseRepository customerBaseRepository;
    @Resource
    private CustomerExtRepository customerExtRepository;
    @Resource
    private CustomerManagerLogRepository customerManagerLogRepository;
    @Resource
    private CustomerBaseStruct customerBaseStruct;
    @Resource
    private CustomerPropertyService customerPropertiesService;
    @Resource
    private CustomerGroupRelationRepository customerGroupRelationRepository;
    @Resource
    private ToolSrvRemote toolSrvRemote;
    @Resource
    private CrmGoodsRemote crmGoodsRemote;
    @Autowired
    private CustomerGroupService customerGroupService;
    @Resource
    private CustomerGroupMapper customerGroupMapper;

    @Resource
    private CustomerInsideStruct customerInsideStruct;

    /**
     * 附属表的档案类
     *
     * <AUTHOR>
     * @date 2025/08/16
     */
    record ExtTypedEntry(long id /*ext_id*/, String jsonValue) {
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IdRequestVO createOrUpdate(CustomerEditReqVO editReqVO) {
        StringUtil.removeSpaces(editReqVO);
        CustomerEditReqVO.CustomerEditBaseReqVO base = editReqVO.getBase();
        if (Objects.isNull(base)) {
            throw new BizException("[customerBaseEditor]createOrUpdate.base must not be null");
        }
        if (null != base.getId()) {    //for updating customer
            return this.update(editReqVO);
        }
        return this.create(editReqVO);  //create new customer
    }

    protected IdRequestVO create(CustomerEditReqVO createVO) {
        CustomerEditReqVO.CustomerEditBaseReqVO createBase = createVO.getBase();
        CustomerBasePO customerBasePO = customerBaseStruct.baseVO2PO(createBase);

        Long managerId = ContextUtil.getManagerId(createVO);
        customerBasePO.setCreatedBy(managerId);
        customerBasePO.setCreatedByName(createVO.getAccountManagerName());
        customerBasePO.setRelatedManagerId(managerId);
        customerBasePO.setRelatedManagerName(createVO.getAccountManagerName());

        this.parseImages(createBase, customerBasePO);
        customerBaseRepository.save(customerBasePO);
        //插入附属表
        Long customerId = customerBasePO.getId();
        List<CustomerExtPO> extPOList = Lists.newArrayList();
        CustomerEditReqVO.CustomerEditExtReqVO createPreferVO = createVO.getPreference();
        this.correctExtObj(createBase.getContactMode(), CustomerExtTypeEnum.CONTACT_MODE.code, extPOList, null);
        this.correctExtObj(createPreferVO.getPreferType(), CustomerExtTypeEnum.PREFER_TYPE.code, extPOList, null);
        this.fillBrandObj(createPreferVO, extPOList, null);
        if (!CollectionUtils.isEmpty(extPOList)) {
            extPOList.forEach(extPO -> extPO.setCustomerId(customerId));
            customerExtRepository.saveBatch(extPOList);
        }
        //通知CRM宽表更新
        customerBaseRepository.noticeUpdateCustomer(customerId);
        return new IdRequestVO(customerId);
    }

    /**
     * 矫正OptionValues为JSON
     *
     * @param optionValues
     * @param type
     * @param extPOList    ext polist
     */
    private void correctExtObj(OptionValues optionValues, Integer type, List<CustomerExtPO> extPOList, Long extId) {
        if (CollectionUtils.isEmpty(optionValues)) {
            return;
        }
        CustomerExtPO customerExtPO = new CustomerExtPO();
        customerExtPO.setId(extId);
        customerExtPO.setType(type);
        customerExtPO.setExtValue(optionValues.toJson());
        extPOList.add(customerExtPO);
    }

    /**
     * 填充品牌对象
     *
     * @param createPreferVO
     * @param extPOList
     */
    private void fillBrandObj(CustomerEditReqVO.CustomerEditExtReqVO createPreferVO, List<CustomerExtPO> extPOList, Long extId) {
        JSONObject brandJSON = JSONObject.of();
        if (null != createPreferVO.getBag()) {
            brandJSON.put(CustomerPreferBrandEnum.BAG.code, createPreferVO.getBag());
        }
        if (null != createPreferVO.getJewel()) {
            brandJSON.put(CustomerPreferBrandEnum.JEWEL.code, createPreferVO.getJewel());
        }
        if (null != createPreferVO.getWatches()) {
            brandJSON.put(CustomerPreferBrandEnum.WATCHES.code, createPreferVO.getWatches());
        }
        if (!brandJSON.isEmpty()) {
            CustomerExtPO customerExtPO = new CustomerExtPO();
            customerExtPO.setId(extId);
            customerExtPO.setType(CustomerExtTypeEnum.PREFER_BRAND.code);
            customerExtPO.setExtValue(brandJSON.toJSONString());
            extPOList.add(customerExtPO);
        }
    }

    private void parseImages(CustomerEditReqVO.CustomerEditBaseReqVO createBase, CustomerBasePO customerBasePO) {
        if (CollectionUtils.isEmpty(createBase.getImages())) {
            return;
        }
        customerBasePO.setImages(String.join(",", createBase.getImages().keys()));
    }

    protected IdRequestVO update(CustomerEditReqVO updateVO) {
        CustomerEditReqVO.CustomerEditBaseReqVO updateBase = updateVO.getBase();
        CustomerBasePO existBasePo = customerBaseRepository.selectById(updateBase.getId()).orElseThrow(() -> new BizException("customer not found by id:" + updateBase.getId()));
        CustomerBasePO customerBasePO = customerBaseStruct.baseVO2PO(updateBase);
        this.parseImages(updateBase, customerBasePO);
        customerBaseRepository.updateById(customerBasePO);
        //更新附属表
        List<CustomerExtPO> extPOS = customerExtRepository.lambdaQuery()
                .eq(CustomerExtPO::getCustomerId, updateBase.getId())
                .list();
        Map<Integer /*type*/, ExtTypedEntry> extTable = extPOS.stream()
                .collect(Collectors.toUnmodifiableMap(CustomerExtPO::getType
                        , extPO -> new ExtTypedEntry(extPO.getId(), extPO.getExtValue())
                        , (n, v) -> v));

        List<CustomerExtPO> extPOList = Lists.newArrayList();
        //首选沟通方式，必填项
        if (!extTable.get(CustomerExtTypeEnum.CONTACT_MODE.code).jsonValue.equals(updateBase.getContactMode().toJson())) {
            this.correctExtObj(updateBase.getContactMode(), CustomerExtTypeEnum.CONTACT_MODE.code, extPOList, extTable.get(CustomerExtTypeEnum.CONTACT_MODE.code).id);
        }
        CustomerEditReqVO.CustomerEditExtReqVO preference = updateVO.getPreference();
        //偏好类型，必填
        if (!extTable.get(CustomerExtTypeEnum.PREFER_TYPE.code).jsonValue.equals(preference.getPreferType().toJson())) {
            this.correctExtObj(preference.getPreferType(), CustomerExtTypeEnum.PREFER_TYPE.code, extPOList, extTable.get(CustomerExtTypeEnum.PREFER_TYPE.code).id);
        }
        //填充品牌信息
        if (extTable.containsKey(CustomerExtTypeEnum.PREFER_BRAND.code)) {
            this.fillBrandObj(preference, extPOList, extTable.get(CustomerExtTypeEnum.PREFER_BRAND.code).id);
        } else {
            this.fillBrandObj(preference, extPOList, null);
        }
        if (!CollectionUtils.isEmpty(extPOList)) {
            extPOList.forEach(extPO -> extPO.setCustomerId(updateBase.getId()));
            customerExtRepository.saveOrUpdateBatch(extPOList);
        }
        //处理宽表字段通知
        if (this.isIsNeedToNotifyCRM(existBasePo, updateBase)) {
            customerBaseRepository.noticeUpdateCustomer(customerBasePO.getId());
        }
        return new IdRequestVO(customerBasePO.getId());
    }

    /**
     * 是否需要通知CRM，更新宽表数据
     *
     * @param existBasePo
     * @param updateBase
     * @return boolean
     */
    private boolean isIsNeedToNotifyCRM(CustomerBasePO existBasePo, CustomerEditReqVO.CustomerEditBaseReqVO updateBase) {
        boolean isNeedToNotifyCRM = false;
        if (!Objects.equals(existBasePo.getMobile(), updateBase.getMobile())) {
            log.info("[customerBaseUpdater].op=update.mobile changed, old={}, new={}", existBasePo.getMobile(), updateBase.getMobile());
            isNeedToNotifyCRM = true;
        }
        if (!Objects.equals(existBasePo.getName(), updateBase.getName())) {
            log.info("[customerBaseUpdater].op=update.name changed, old={}, new={}", existBasePo.getName(), updateBase.getName());
            isNeedToNotifyCRM = true;
        }
        if (!Objects.equals(existBasePo.getAgeGroup(), updateBase.getAgeGroup().requireSingleCode())) {
            log.info("[customerBaseUpdater].op=update.ageGroup changed, old={}, new={}", existBasePo.getAgeGroup(), updateBase.getAgeGroup().requireSingleCode());
            isNeedToNotifyCRM = true;
        }
        //变更了出售意向
        if (!Objects.equals(existBasePo.getOutputIntention(), updateBase.getMembership().getOutputIntention().requireSingleCode())) {
            log.info("[customerBaseUpdater].op=update.outputIntention changed, old={}, new={}", existBasePo.getOutputIntention(), updateBase.getMembership().getOutputIntention().requireSingleCode());
            isNeedToNotifyCRM = true;
        }
        //变更了购买意向
        if (!Objects.equals(existBasePo.getPurchaseIntention(), updateBase.getMembership().getPurchaseIntention().requireSingleCode())) {
            log.info("[customerBaseUpdater].op=update.purchaseIntention changed, old={}, new={}", existBasePo.getPurchaseIntention(), updateBase.getMembership().getPurchaseIntention().requireSingleCode());
            isNeedToNotifyCRM = true;
        }
        return isNeedToNotifyCRM;
    }

    @Override
    public CustomerShowRespVO query(CustomerShowReqVO showReqVO) {
        Objects.requireNonNull(showReqVO.getCustomerId(), "customerId must not be null");
        CustomerBasePO basePO = customerBaseRepository.getOptById(showReqVO.getCustomerId()).orElseThrow(() -> new RuntimeException("customer not found by id:" + showReqVO.getCustomerId()));
        CustomerPropertyRespVO properties = customerPropertiesService.getCustomerProperty();
        CustomerShowRespVO.CustomerShowBaseRespVO baseVO = customerBaseStruct.PO2BaseVO(basePO, properties.getProperties());
        //解析图片数据
        if (StringUtils.isNotBlank(basePO.getImages())) {
            String[] imgSplit = basePO.getImages().split(",");
            ImageValues imageValues = new ImageValues();
            for (String imgKey : imgSplit) {
                String signedUrl = toolSrvRemote.fileSignedUrl(imgKey);
                imageValues.add(new ImageValues.ImageRecord(imgKey, signedUrl));
            }
            baseVO.setImages(imageValues);
        }
        //附属表数据
        List<CustomerExtPO> extPOS = customerExtRepository.lambdaQuery().eq(CustomerExtPO::getCustomerId, showReqVO.getCustomerId()).list();
        Map<Integer /*type*/, String /*JSON value*/> extTypeTable = extPOS.stream().collect(Collectors.toMap(CustomerExtPO::getType, CustomerExtPO::getExtValue, (n, v) -> v));

        CustomerShowRespVO.CustomerShowExtRespVO preference = new CustomerShowRespVO.CustomerShowExtRespVO();
        if (MapUtils.isNotEmpty(extTypeTable)) {
            String contactModeJSON = extTypeTable.get(CustomerExtTypeEnum.CONTACT_MODE.code);
            if (StringUtils.isNotBlank(contactModeJSON)) {
                OptionValues contactMode = JSONObject.parseObject(contactModeJSON, OptionValues.class);
                baseVO.setContactMode(contactMode);
            }
            String preferTypeJSON = extTypeTable.get(CustomerExtTypeEnum.PREFER_TYPE.code);
            if (StringUtils.isNotBlank(preferTypeJSON)) {
                OptionValues preferType = JSONObject.parseObject(preferTypeJSON, OptionValues.class);
                preference.setPreferType(preferType);
            }
            String preferBrandJSON = extTypeTable.get(CustomerExtTypeEnum.PREFER_BRAND.code);
            if (StringUtils.isNotBlank(preferBrandJSON)) {
                JSONObject preferBrand = JSONObject.parseObject(preferBrandJSON);
                preference.setBag(preferBrand.getString(CustomerPreferBrandEnum.BAG.code));
                preference.setJewel(preferBrand.getString(CustomerPreferBrandEnum.JEWEL.code));
                preference.setWatches(preferBrand.getString(CustomerPreferBrandEnum.WATCHES.code));
            }
        }
        return new CustomerShowRespVO().setBase(baseVO).setPreference(preference);
    }

    @Override
    public List<CustomerInfoInHomeRespVO> batchQueryCustomerInfoInHome(CustomerBatchQueryInHomeReqVO reqVO) {
        if (CollectionUtils.isEmpty(reqVO.getIds())) {
            return List.of();
        }
        List<CustomerBasePO> customerBasePOList = customerBaseRepository.lambdaQuery().in(CustomerBasePO::getId, reqVO.getIds()).list();
        if (CollectionUtils.isEmpty(customerBasePOList)) {
            log.warn("[batchQueryCustomerInfoInHome].basePOList is empty, ids:{}", reqVO.getIds());
            return List.of();
        }
        CustomerPropertyRespVO properties = customerPropertiesService.getCustomerProperty();
        return customerBasePOList.stream()
                .map(customerBasePO -> {
                    CustomerInfoInHomeRespVO inHomeVO = customerBaseStruct.PO2HomeBatchVO(customerBasePO, properties.getProperties());
                    Optional<CustomerGroupRelationPO> groupRelationOpt = customerGroupRelationRepository.selectByManageIdAndCustomerId(customerBasePO.getRelatedManagerId(), customerBasePO.getId());
                    groupRelationOpt.ifPresent(relationPO -> {
                        if (StringUtils.isBlank(relationPO.getGroupId())) {
                            return;
                        }
                        //组ID存储为JSON数组
                        List<String> groupIdList = JSON.parseArray(relationPO.getGroupId(), String.class);
                        List<CustomerGroupPO> groupPOS = customerGroupMapper.selectBatchIds(groupIdList);
                        if (CollectionUtils.isEmpty(groupPOS)) {
                            return;
                        }
                        OptionValues optionValues = new OptionValues();
                        for (CustomerGroupPO groupPO : groupPOS) {
                            optionValues.add(new OptionValues.OptionRecord(String.valueOf(groupPO.getId()), groupPO.getGroupName()));
                        }
                        inHomeVO.setCustomerGroup(optionValues);
                    });
                    return inHomeVO;
                })
                .toList();
    }

    @Override
    public PagingList<CustomerMisPageRespVO> page(CustomerMisPageReqVO reqVO) {
        Page<CustomerBasePO> pageResult = customerBaseRepository.pageForMIS(reqVO);
        if (null == pageResult || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PagingList<>();
        }
        List<CustomerBasePO> basePOS = pageResult.getRecords();
        //从CRM端获取商品收藏数量
        CrmGoodsCntDTO goodsCntDTO = crmGoodsRemote.getGoodsCnt(basePOS.stream().map(CustomerBasePO::getId).toList());
        CustomerPropertyRespVO properties = customerPropertiesService.getCustomerProperty();
        List<CustomerMisPageRespVO> res = basePOS.stream()
                .map(po -> {
                    CustomerMisPageRespVO misPageVO = customerBaseStruct.PO2MisPageVO(po, properties.getProperties());
                    misPageVO.setHasWechat(StringUtils.isNotBlank(po.getWechatId()));
                    misPageVO.setCollectCnt(goodsCntDTO.cntTable().getOrDefault(po.getId(), 0));
                    return misPageVO;
                }).toList();

        return new PagingList<>(pageResult.getTotal(), pageResult.getSize(), pageResult.getCurrent(), pageResult.hasNext(), res);
    }

    @Override
    public void transfer(CustomerMisTransferReqVO reqVO) {
        if (null == reqVO.getCustomerId() || null == reqVO.getManagerId()) {
            throw new BizException("customerId and managerId must not be null");
        }
        CustomerBasePO existPO = customerBaseRepository.selectById(reqVO.getCustomerId()).orElseThrow(() -> new BizException("customer not found by id:" + reqVO.getCustomerId()));
        boolean updated = customerBaseRepository.updateRelatedManager(reqVO.getCustomerId(), reqVO.getManagerId(), reqVO.getManagerName());
        if (!updated) {
            log.warn("[customerBaseUpdater].op=transfer, no need to update, customerId={}, managerId={}", reqVO.getCustomerId(), reqVO.getManagerId());
            return;
        }
        //插入变更日志
        Long accountManagerId = ContextUtil.getManagerId(reqVO);
        CustomerManagerLogPO customerManagerLogPO = new CustomerManagerLogPO()
                .setCustomerId(reqVO.getCustomerId())
                .setOldRelatedManagerId(existPO.getRelatedManagerId())
                .setOldRelatedManagerName(existPO.getRelatedManagerName())
                .setNewRelatedManagerId(reqVO.getManagerId())
                .setNewRelatedManagerName(reqVO.getManagerName())
                .setUpdatedBy(accountManagerId);
        customerManagerLogRepository.save(customerManagerLogPO);
        customerBaseRepository.noticeUpdateCustomer(reqVO.getCustomerId());
    }

    @Override
    public CustomerBaseInsideRespVO queryCustomerBaseInfo(CustomerBaseInsideReqVO reqVO) {
        Objects.requireNonNull(reqVO.getCustomerId(), "customerId must not be null");
        CustomerBasePO basePO = customerBaseRepository.getOptById(reqVO.getCustomerId()).orElseThrow(() -> new RuntimeException("customer not found by id:" + reqVO.getCustomerId()));
        return customerInsideStruct.PO2InsideVO(basePO, customerPropertiesService.getCustomerProperty().getProperties());
    }
}
