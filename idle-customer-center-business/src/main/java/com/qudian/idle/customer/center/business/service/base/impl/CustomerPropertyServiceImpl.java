package com.qudian.idle.customer.center.business.service.base.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.base.Throwables;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.idle.customer.center.business.service.base.CustomerPropertyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Huang
 * @date 2025/8/13
 */
@Service
@Slf4j
public class CustomerPropertyServiceImpl implements CustomerPropertyService {
    @Value("${customer.properties:}")
    private String customerProperties;

    @Override
    public CustomerPropertyRespVO getCustomerProperty() {
        //log.info("op=start_CustomerConfigAppFacadeImpl.getConfigList, customerProperties={}", customerProperties);
        CustomerPropertyRespVO customerConfigRespVO = null;
        try {
            customerConfigRespVO = JSON.parseObject(customerProperties, CustomerPropertyRespVO.class);
        } catch (Exception e) {
            log.error("[op=CustomerPropertyAppFacadeImpl.getConfigList error]错误信息: {}", Throwables.getStackTraceAsString(e));
        }
        return customerConfigRespVO;
    }
}
