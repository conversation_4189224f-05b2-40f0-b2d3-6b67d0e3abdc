package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerBaseInsideRespVO;
import com.qudian.idle.customer.center.api.vo.share.OptionValues;
import com.qudian.idle.customer.center.api.vo.share.Property;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import org.mapstruct.*;

import java.util.List;
import java.util.function.Function;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.assembler.CustomerBaseVO2PO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Mapper
public interface CustomerInsideStruct {

    @Mappings({
            @Mapping(source = "po.relatedManagerId", target = "managerId"),
            @Mapping(source = "po.outputIntention", target = "outputIntention", qualifiedByName = "outputIntentionToString"),
            @Mapping(source = "po.membershipType", target = "membershipType", qualifiedByName = "membershipTypeToString"),
            @Mapping(source = "po.purchaseIntention", target = "purchaseIntention", qualifiedByName = "purchaseIntentionToString"),
            @Mapping(source = "po.ageGroup", target = "ageGroup", qualifiedByName = "purchaseIntentionToString"),
            @Mapping(source = "po.gender", target = "gender", qualifiedByName = "purchaseIntentionToString"),
    })
    CustomerBaseInsideRespVO PO2InsideVO(CustomerBasePO po, @Context CustomerPropertyVO propertyVO);

    enum FieldType {
        GENDER(CustomerPropertyVO::getGender),
        AGE_GROUP(CustomerPropertyVO::getAgeGroup),
        SOURCE(CustomerPropertyVO::getSource),
        HOME_VISIT_STATUS(CustomerPropertyVO::getHomeVisitStatus),
        OUTPUT_INTENTION(CustomerPropertyVO::getOutputIntention),
        MEMBERSHIP_TYPE(CustomerPropertyVO::getMembershipType),
        PURCHASE_INTENTION(CustomerPropertyVO::getPurchaseIntention),
        CUSTOMER_TYPE(CustomerPropertyVO::getCustomerType);

        Function<CustomerPropertyVO, List<Property>> properties;

        FieldType(Function<CustomerPropertyVO, List<Property>> propertyGetter) {
            this.properties = propertyGetter;
        }
    }

    /**************转化为供展示字符串**************/
    default String convertToString(String code, CustomerPropertyVO propertyVO, FieldType fieldType) {
        if (code == null) {
            return null;
        }
        return findMatchingProperty(code, fieldType.properties.apply(propertyVO)).title();
    }

    @Named("sourceModeToString")
    default String sourceModeToString(String code, @Context CustomerPropertyVO propertyVO) {
        return convertToString(code, propertyVO, FieldType.SOURCE);
    }

    @Named("membershipTypeToString")
    default String membershipTypeToString(String code, @Context CustomerPropertyVO propertyVO) {
        return convertToString(code, propertyVO, FieldType.MEMBERSHIP_TYPE);
    }

    @Named("purchaseIntentionToString")
    default String purchaseIntentionToString(String code, @Context CustomerPropertyVO propertyVO) {
        return convertToString(code, propertyVO, FieldType.PURCHASE_INTENTION);
    }

    @Named("outputIntentionToString")
    default String outputIntentionToString(String code, @Context CustomerPropertyVO propertyVO) {
        return convertToString(code, propertyVO, FieldType.OUTPUT_INTENTION);
    }


    private OptionValues findMatchingProperty(String code, List<Property> properties) {
        if (properties == null || properties.isEmpty()) {
            return new OptionValues();
        }
        for (Property prop : properties) {
            if (code.equals(prop.getCode())) {
                return OptionValues.of(code, prop.getTitle());
            }
        }
        return new OptionValues();
    }
}
