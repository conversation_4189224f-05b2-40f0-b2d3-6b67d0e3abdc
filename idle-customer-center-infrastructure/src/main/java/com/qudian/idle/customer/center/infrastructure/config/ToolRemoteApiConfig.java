package com.qudian.idle.customer.center.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>文件名称:com.qudian.lme.driver.common.config.ToolRemoteApiConfig</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/11/10
 */
@Data
@Configuration
@ConfigurationProperties("lme.tool")
public class ToolRemoteApiConfig {
    /**
     * 本地："http://lme-dev-tool-srv.quplusplus.net"
     */
    private String host;

    /**
     * oss,gcs
     */
    private String storeProvider = "oss";
    /**
     * 短信发送
     */
    private String smsNotify = "/v1/sms/send";

    /**
     * 短链接生成
     */
    private String shortCreate = "/short/create";

    private String emailNotify = "/v1/email/send";

    //上传文件
    private String uploadFileNotify = "/v1/storage/upload";
    private String signedUrl = "/v1/storage/signedurl";
    private String batchSignedUrl = "/v1/storage/batchSignedurl";
    private String regionUrl = "/v1/region/query";

    // 文件加签
    private String fileSigned = "/v1/storage/signedurl/unified";

    private String regionAddressFile = "/v1/region/address";
    private String regionAddressFileV2 = "/v2/region/address";
    private final String addressValidUrl = "/v2/map/address/validation";


    private String regionQueryByPostcode = "/v1/region/query";
    private String regionQueryByPostcodeV2 = "/v2/region/query";

    private String regionCenterByPostcode = "/v1/region/center/query";

    private String regionCenterList = "/v1/region/center/list";

    /**
     * 地图-地址转换-google
     */
    private String googleGeoCoding = "/v1/map/geocoding";
    /**
     * 绑定隐私号
     */
    private String axbBind = "/v1/axb/bind";
    /**
     * 查询axb通话记录
     */
    private String axbCallLog = "/v1/axb/call/log";
    /**
     * 校验电话号码格式
     */
    private String checkPhone = "/v1/sms/phone/check";

    /**
     * 创建飞书审批实例
     */
    private String createApproval ="/v1/lark/approval/createApprovalInstance";

    /**
     * 上传飞书审批附件 for 创建飞书审批实例
     */
    private String approvalUploadFile ="/v1/lark/approval/uploadFile";
    /**
     * 获取多语言
     */
    private String languageList = "/v1/language/getList";

    private String faceIDVerify = "/v1/face/verify";
    private String ocrGet = "/v1/ocr/get";

    private String ossUploadUrl = "/oss/upload/private";
}
