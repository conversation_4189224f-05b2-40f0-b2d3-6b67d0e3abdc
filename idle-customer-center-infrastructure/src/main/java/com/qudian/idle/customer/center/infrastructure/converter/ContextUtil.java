package com.qudian.idle.customer.center.infrastructure.converter;

import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonObject;
import com.qudian.idle.customer.center.api.vo.share.BaseCrmReqVO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> Huang
 * @date 2025/8/14
 */
@Slf4j
public class ContextUtil {

    //Todo: @hfq 从上下文获取
    public static Long getUserId(){
        return 0l;
    }


    public static Long getUserId(BaseRequestVO baseRequestVO){
        if(Objects.nonNull(baseRequestVO.getUserId())){
            return baseRequestVO.getUserId();
        }
        return 0l;
    }

    public static Long getManagerId(BaseCrmReqVO crmReqVO){
        if (StringUtils.isBlank(crmReqVO.getAccountManagerId())) {
            log.error("[ContextUtil].getManagerId, accountManagerId is blank, crmReqVO={}", JSONObject.toJSONString(crmReqVO));
            throw new RuntimeException("accountManagerId is blank");
            //return 0L;
        }
        return Long.parseLong(crmReqVO.getAccountManagerId());
    }

    public static String getUserName(){
        return "defaultUser";
    }

}
