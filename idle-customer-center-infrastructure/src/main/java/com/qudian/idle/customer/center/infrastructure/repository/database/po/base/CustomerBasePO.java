package com.qudian.idle.customer.center.infrastructure.repository.database.po.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_base")
public class CustomerBasePO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    private String mobile;
    private String wechatId; //客户微信号
    private Integer gender; //性别 1(男), 2(女)
    private Integer ageGroup; //客户年龄段: 1(<24), 2(25-34), 3(35-44), 4(45-54), 5(55+)
    private Integer source; //客户来源: 1(线上引流), 2(线下渠道)
    private Integer homeVisitStatus; //是否接受上门: 1(接受上门), 2(不接受上门)
    private Integer outputIntention; //客户出售意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    private Integer membershipType; //会员类型: 1(普通), 2（VIP会员）
    private Integer purchaseIntention; //客户购买意向: 1(高意向), 2(中意向), 3(低意向), 4(无意向), 5(不确定)
    private String customerType; //客户类型：1（个人），2（公司）
    private String province; //地址-省份
    private String city; //地址-城市
    private String district; //地址-区域
    private String detailAddress; //地址-详细地址
    private String location; //地址-经纬度
    private String remark; //备注
    private String images; //备注图
    private Integer status; //状态 0-正常 1-禁用
    private Long createdBy; //创建人ID
    private String createdByName; //创建人姓名
    private Long relatedManagerId; //关联的客户经理
    private String relatedManagerName; //关联的客户经理姓名
    private Date relatedManagerUpdatedTime; //关联修改时间
    @TableLogic
    private Integer deleteFlag; //是否删除 1已删除 0 未删除
    private LocalDateTime createdTime; //创建时间
    private LocalDateTime updatedTime; //修改时间
}
