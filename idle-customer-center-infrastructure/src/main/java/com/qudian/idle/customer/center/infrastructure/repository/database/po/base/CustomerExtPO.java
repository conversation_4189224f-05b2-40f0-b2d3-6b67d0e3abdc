package com.qudian.idle.customer.center.infrastructure.repository.database.po.base;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerExtPO</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("customer_ext")
public class CustomerExtPO {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long customerId; //客户ID
    private Integer type; //附属类型: 1(沟通方式), 2(偏好类型), 3(偏好品牌)
    private String extValue; //附属值
    @TableLogic
    private Integer deleteFlag; //是否删除 1已删除 0 未删除
    private LocalDateTime createdTime; //创建时间
    private LocalDateTime updatedTime; //修改时间
}
