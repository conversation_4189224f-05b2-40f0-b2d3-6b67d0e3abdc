package com.qudian.idle.customer.center.infrastructure.repository.rpc.dto.tool;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class FileSignedReqDTO implements Serializable {

    /**
     * oss : 阿里云
     * gcs : 谷歌云
     */
    @JSONField(name = "provider")
    private String provider;

    /**
     * 存储key
     */
    @JSONField(name = "object_key")
    private String objectKey;

    /**
     * 过期时间,单位秒
     */
    @JSONField(name = "expire_in")
    private Integer expireIn;


}
