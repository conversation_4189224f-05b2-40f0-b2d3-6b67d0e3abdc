package com.qudian.idle.customer.center.infrastructure.repository.rpc.dto.tool;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class FileSignedRespDTO implements Serializable {

    /**
     * 过期时间
     */
    @JSONField(name = "expired_at")
    private String expiredAt;

    /**
     * object Key
     */
    @JSONField(name = "object_key")
    private String objectKey;

    /**
     * 加签地址
     */
    @JSONField(name = "signed_url")
    private String signedUrl;
}
