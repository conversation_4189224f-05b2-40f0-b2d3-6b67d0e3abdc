package com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Throwables;
import com.qudian.idle.crm.web.api.dto.base.CommonRespDTO;
import com.qudian.idle.crm.web.api.dto.req.GoodsCountReqDTO;
import com.qudian.idle.crm.web.api.dto.resp.GoodsCountRespDTO;
import com.qudian.idle.crm.web.api.enums.BizCodeEnum;
import com.qudian.idle.crm.web.api.enums.SourceEnum;
import com.qudian.idle.crm.web.api.service.GoodsFacade;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.dto.crm.CrmGoodsCntDTO;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.CrmGoodsRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.impl.CrmGoodsRemoteImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/18
 */
@Slf4j
@Component
public class CrmGoodsRemoteImpl implements CrmGoodsRemote {
    @DubboReference(protocol = "tri", version = "1.0.0")
    private GoodsFacade crmGoodsFacade;

    @Override
    public CrmGoodsCntDTO getGoodsCnt(List<Long> customerIds) {
        CommonRespDTO<List<GoodsCountRespDTO>> cntRespDTO = null;
        try {
            GoodsCountReqDTO goodsCountReqDTO = new GoodsCountReqDTO(customerIds);
            goodsCountReqDTO.setBizCode(BizCodeEnum.idle.getBizCode());
            goodsCountReqDTO.setSource(SourceEnum.USER.getCode());
            cntRespDTO = crmGoodsFacade.countGoodsBy(goodsCountReqDTO);
        } catch (Exception e) {
            log.error("crmGoodsFacade.countGoodsBy:ids{}, error: {}", customerIds, Throwables.getStackTraceAsString(e));
            return new CrmGoodsCntDTO(Maps.newHashMap());
        }
        if(!cntRespDTO.isSuccess() || null == cntRespDTO.getData()){
            log.error("crmGoodsFacade.countGoodsBy:ids{}, resp: {}", customerIds, JSONObject.toJSONString(cntRespDTO));
            return new CrmGoodsCntDTO(Maps.newHashMap());
        }
        Map<Long, Integer> cntTable = cntRespDTO.getData().stream().collect(Collectors.toMap(GoodsCountRespDTO::getCustomerId, GoodsCountRespDTO::getCount, (n, v) -> v));
        return new CrmGoodsCntDTO(cntTable);
    }

}
