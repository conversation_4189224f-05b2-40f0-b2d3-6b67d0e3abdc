package com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.impl;

import com.alibaba.fastjson2.JSONObject;
import com.qudian.idle.customer.center.infrastructure.config.ToolRemoteApiConfig;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.dto.tool.FileSignedReqDTO;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.dto.tool.FileSignedRespDTO;
import com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.ToolSrvRemote;
import com.qudian.lme.base.vo.BaseResponseVo;
import com.qudian.pdt.toolkit.common.enums.ExceptionEnum;
import com.qudian.pdt.toolkit.common.exception.BizException;
import com.qudian.pdt.toolkit.http.core.client.DefaultHttpClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>文件名称:com.qudian.idle.customer.center.infrastructure.repository.rpc.remote.impl.ToolSrvRemoteImpl</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/18
 */
@Component
@Slf4j
public class ToolSrvRemoteImpl implements ToolSrvRemote {
    @Resource
    private ToolRemoteApiConfig toolRemoteApiConfig;
    @Resource
    private DefaultHttpClient toolHttpClient;

    @Override
    public String fileSignedUrl(String objectKey) {
        if (StringUtils.isBlank(objectKey)) {
            return objectKey;
        }
        try {
            FileSignedReqDTO fileSignedReqDTO = new FileSignedReqDTO();
            fileSignedReqDTO.setObjectKey(objectKey);
            fileSignedReqDTO.setProvider(toolRemoteApiConfig.getStoreProvider());
            fileSignedReqDTO.setExpireIn(3600);

            String url = toolRemoteApiConfig.getHost() + toolRemoteApiConfig.getFileSigned();
            HttpUrl.Builder builder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
            builder.addQueryParameter("object_key",objectKey);
            builder.addQueryParameter("provider", toolRemoteApiConfig.getStoreProvider());
            builder.addQueryParameter("expire_in", String.valueOf(3600));
            BaseResponseVo baseResponseVo = toolHttpClient.get(builder);
            log.info("文件加签,加签:{}", JSONObject.toJSONString(baseResponseVo));
            if (!Integer.valueOf(200).equals(baseResponseVo.getCode())) {
                throw new BizException(ExceptionEnum.SECOND_PARTY_ERROR, baseResponseVo.getMessage());
            }
            JSONObject jsonData = (JSONObject) baseResponseVo.getData();
            FileSignedRespDTO respDTO = jsonData.toJavaObject(FileSignedRespDTO.class);
            if (respDTO == null) {
                log.info("文件加签,resp.data为空!");
                return "";
            }
            return respDTO.getSignedUrl();
        } catch (Exception e) {
            log.error("文件加签,发送请求异常!", e);
            throw new BizException(ExceptionEnum.SECOND_PARTY_ERROR);
        }
    }
}
