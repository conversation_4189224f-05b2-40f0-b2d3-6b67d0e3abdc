package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.api.vo.share.OptionValues;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import java.time.format.DateTimeFormatter;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T15:49:27+0800",
    comments = "version: 1.5.5.Final, compiler: java<PERSON>, environment: Java 17 (Oracle Corporation)"
)
@Component
public class CustomerBaseStructImpl implements CustomerBaseStruct {

    private final DateTimeFormatter dateTimeFormatter_yyyy年MM月dd日_HH_mm_11363607979 = DateTimeFormatter.ofPattern( "yyyy年MM月dd日 HH:mm" );

    @Override
    public CustomerBasePO baseVO2PO(CustomerEditReqVO.CustomerEditBaseReqVO reqVO) {
        if ( reqVO == null ) {
            return null;
        }

        CustomerBasePO customerBasePO = new CustomerBasePO();

        if ( reqVO.getGender() != null ) {
            customerBasePO.setGender( Integer.parseInt( singleCode( reqVO.getGender() ) ) );
        }
        if ( reqVO.getAgeGroup() != null ) {
            customerBasePO.setAgeGroup( Integer.parseInt( singleCode( reqVO.getAgeGroup() ) ) );
        }
        if ( reqVO.getSource() != null ) {
            customerBasePO.setSource( Integer.parseInt( singleCode( reqVO.getSource() ) ) );
        }
        if ( reqVO.getHomeVisitStatus() != null ) {
            customerBasePO.setHomeVisitStatus( Integer.parseInt( singleCode( reqVO.getHomeVisitStatus() ) ) );
        }
        customerBasePO.setCustomerType( singleCode( reqVO.getCustomerType() ) );
        OptionValues outputIntention = reqVOMembershipOutputIntention( reqVO );
        if ( outputIntention != null ) {
            customerBasePO.setOutputIntention( Integer.parseInt( singleCode( outputIntention ) ) );
        }
        OptionValues membershipType = reqVOMembershipMembershipType( reqVO );
        if ( membershipType != null ) {
            customerBasePO.setMembershipType( Integer.parseInt( singleCode( membershipType ) ) );
        }
        OptionValues purchaseIntention = reqVOMembershipPurchaseIntention( reqVO );
        if ( purchaseIntention != null ) {
            customerBasePO.setPurchaseIntention( Integer.parseInt( singleCode( purchaseIntention ) ) );
        }
        customerBasePO.setProvince( reqVOAddressProvince( reqVO ) );
        customerBasePO.setCity( reqVOAddressCity( reqVO ) );
        customerBasePO.setDistrict( reqVOAddressDistrict( reqVO ) );
        customerBasePO.setDetailAddress( reqVOAddressDetailAddress( reqVO ) );
        customerBasePO.setId( reqVO.getId() );
        customerBasePO.setName( reqVO.getName() );
        customerBasePO.setMobile( reqVO.getMobile() );
        customerBasePO.setWechatId( reqVO.getWechatId() );
        customerBasePO.setRemark( reqVO.getRemark() );

        return customerBasePO;
    }

    @Override
    public CustomerShowRespVO.CustomerShowBaseRespVO PO2BaseVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerShowRespVO.CustomerShowBaseRespVO customerShowBaseRespVO = new CustomerShowRespVO.CustomerShowBaseRespVO();

        customerShowBaseRespVO.setMembership( customerBasePOToCustomerShowMembershipReqVO( po, propertyVO ) );
        customerShowBaseRespVO.setAddress( customerBasePOToCustomerShowAddressReqVO( po, propertyVO ) );
        customerShowBaseRespVO.setGender( genderOptionValues( String.valueOf( po.getGender() ), propertyVO ) );
        customerShowBaseRespVO.setAgeGroup( ageGroupOptionValues( String.valueOf( po.getAgeGroup() ), propertyVO ) );
        customerShowBaseRespVO.setSource( sourceModeOptionValues( String.valueOf( po.getSource() ), propertyVO ) );
        customerShowBaseRespVO.setHomeVisitStatus( homeVisitStatusOptionValues( String.valueOf( po.getHomeVisitStatus() ), propertyVO ) );
        customerShowBaseRespVO.setCustomerType( customerTypeOptionValues( po.getCustomerType(), propertyVO ) );
        customerShowBaseRespVO.setId( po.getId() );
        customerShowBaseRespVO.setName( po.getName() );
        customerShowBaseRespVO.setMobile( po.getMobile() );
        customerShowBaseRespVO.setWechatId( po.getWechatId() );
        customerShowBaseRespVO.setRemark( po.getRemark() );

        return customerShowBaseRespVO;
    }

    @Override
    public CustomerInfoInHomeRespVO PO2HomeBatchVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerInfoInHomeRespVO customerInfoInHomeRespVO = new CustomerInfoInHomeRespVO();

        customerInfoInHomeRespVO.setGender( genderOptionValues( String.valueOf( po.getGender() ), propertyVO ) );
        customerInfoInHomeRespVO.setAgeGroup( ageGroupOptionValues( String.valueOf( po.getAgeGroup() ), propertyVO ) );
        customerInfoInHomeRespVO.setOutputIntention( outputIntentionOptionValues( String.valueOf( po.getOutputIntention() ), propertyVO ) );
        customerInfoInHomeRespVO.setMembershipType( membershipTypeOptionValues( String.valueOf( po.getMembershipType() ), propertyVO ) );
        customerInfoInHomeRespVO.setPurchaseIntention( purchaseIntentionOptionValues( String.valueOf( po.getPurchaseIntention() ), propertyVO ) );
        customerInfoInHomeRespVO.setId( po.getId() );
        customerInfoInHomeRespVO.setName( po.getName() );
        customerInfoInHomeRespVO.setMobile( po.getMobile() );
        customerInfoInHomeRespVO.setWechatId( po.getWechatId() );

        return customerInfoInHomeRespVO;
    }

    @Override
    public CustomerMisPageRespVO PO2MisPageVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerMisPageRespVO customerMisPageRespVO = new CustomerMisPageRespVO();

        if ( po.getSource() != null ) {
            customerMisPageRespVO.setSource( sourceModeToString( String.valueOf( po.getSource() ), propertyVO ) );
        }
        if ( po.getOutputIntention() != null ) {
            customerMisPageRespVO.setOutputIntention( outputIntentionToString( String.valueOf( po.getOutputIntention() ), propertyVO ) );
        }
        if ( po.getMembershipType() != null ) {
            customerMisPageRespVO.setMembershipType( membershipTypeToString( String.valueOf( po.getMembershipType() ), propertyVO ) );
        }
        if ( po.getPurchaseIntention() != null ) {
            customerMisPageRespVO.setPurchaseIntention( purchaseIntentionToString( String.valueOf( po.getPurchaseIntention() ), propertyVO ) );
        }
        customerMisPageRespVO.setManagerId( po.getRelatedManagerId() );
        customerMisPageRespVO.setManagerName( po.getRelatedManagerName() );
        if ( po.getCreatedTime() != null ) {
            customerMisPageRespVO.setCreatedTime( dateTimeFormatter_yyyy年MM月dd日_HH_mm_11363607979.format( po.getCreatedTime() ) );
        }
        customerMisPageRespVO.setId( po.getId() );
        customerMisPageRespVO.setName( po.getName() );
        customerMisPageRespVO.setMobile( po.getMobile() );

        return customerMisPageRespVO;
    }

    private OptionValues reqVOMembershipOutputIntention(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditMembershipReqVO membership = customerEditBaseReqVO.getMembership();
        if ( membership == null ) {
            return null;
        }
        OptionValues outputIntention = membership.getOutputIntention();
        if ( outputIntention == null ) {
            return null;
        }
        return outputIntention;
    }

    private OptionValues reqVOMembershipMembershipType(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditMembershipReqVO membership = customerEditBaseReqVO.getMembership();
        if ( membership == null ) {
            return null;
        }
        OptionValues membershipType = membership.getMembershipType();
        if ( membershipType == null ) {
            return null;
        }
        return membershipType;
    }

    private OptionValues reqVOMembershipPurchaseIntention(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditMembershipReqVO membership = customerEditBaseReqVO.getMembership();
        if ( membership == null ) {
            return null;
        }
        OptionValues purchaseIntention = membership.getPurchaseIntention();
        if ( purchaseIntention == null ) {
            return null;
        }
        return purchaseIntention;
    }

    private String reqVOAddressProvince(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String province = address.getProvince();
        if ( province == null ) {
            return null;
        }
        return province;
    }

    private String reqVOAddressCity(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String city = address.getCity();
        if ( city == null ) {
            return null;
        }
        return city;
    }

    private String reqVOAddressDistrict(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String district = address.getDistrict();
        if ( district == null ) {
            return null;
        }
        return district;
    }

    private String reqVOAddressDetailAddress(CustomerEditReqVO.CustomerEditBaseReqVO customerEditBaseReqVO) {
        if ( customerEditBaseReqVO == null ) {
            return null;
        }
        CustomerEditReqVO.CustomerEditAddressReqVO address = customerEditBaseReqVO.getAddress();
        if ( address == null ) {
            return null;
        }
        String detailAddress = address.getDetailAddress();
        if ( detailAddress == null ) {
            return null;
        }
        return detailAddress;
    }

    protected CustomerShowRespVO.CustomerShowMembershipReqVO customerBasePOToCustomerShowMembershipReqVO(CustomerBasePO customerBasePO, CustomerPropertyVO propertyVO) {
        if ( customerBasePO == null ) {
            return null;
        }

        CustomerShowRespVO.CustomerShowMembershipReqVO customerShowMembershipReqVO = new CustomerShowRespVO.CustomerShowMembershipReqVO();

        customerShowMembershipReqVO.setOutputIntention( outputIntentionOptionValues( String.valueOf( customerBasePO.getOutputIntention() ), propertyVO ) );
        customerShowMembershipReqVO.setMembershipType( membershipTypeOptionValues( String.valueOf( customerBasePO.getMembershipType() ), propertyVO ) );
        customerShowMembershipReqVO.setPurchaseIntention( purchaseIntentionOptionValues( String.valueOf( customerBasePO.getPurchaseIntention() ), propertyVO ) );

        return customerShowMembershipReqVO;
    }

    protected CustomerShowRespVO.CustomerShowAddressReqVO customerBasePOToCustomerShowAddressReqVO(CustomerBasePO customerBasePO, CustomerPropertyVO propertyVO) {
        if ( customerBasePO == null ) {
            return null;
        }

        CustomerShowRespVO.CustomerShowAddressReqVO customerShowAddressReqVO = new CustomerShowRespVO.CustomerShowAddressReqVO();

        customerShowAddressReqVO.setProvince( customerBasePO.getProvince() );
        customerShowAddressReqVO.setCity( customerBasePO.getCity() );
        customerShowAddressReqVO.setDistrict( customerBasePO.getDistrict() );
        customerShowAddressReqVO.setDetailAddress( customerBasePO.getDetailAddress() );

        return customerShowAddressReqVO;
    }
}
