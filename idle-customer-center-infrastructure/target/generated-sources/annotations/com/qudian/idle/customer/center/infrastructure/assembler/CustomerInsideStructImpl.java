package com.qudian.idle.customer.center.infrastructure.assembler;

import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerBaseInsideRespVO;
import com.qudian.idle.customer.center.infrastructure.repository.database.po.base.CustomerBasePO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-19T11:00:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17 (Oracle Corporation)"
)
@Component
public class CustomerInsideStructImpl implements CustomerInsideStruct {

    @Override
    public CustomerBaseInsideRespVO PO2InsideVO(CustomerBasePO po, CustomerPropertyVO propertyVO) {
        if ( po == null ) {
            return null;
        }

        CustomerBaseInsideRespVO customerBaseInsideRespVO = new CustomerBaseInsideRespVO();

        customerBaseInsideRespVO.setManagerId( po.getRelatedManagerId() );
        if ( po.getOutputIntention() != null ) {
            customerBaseInsideRespVO.setOutputIntention( outputIntentionToString( String.valueOf( po.getOutputIntention() ), propertyVO ) );
        }
        if ( po.getMembershipType() != null ) {
            customerBaseInsideRespVO.setMembershipType( membershipTypeToString( String.valueOf( po.getMembershipType() ), propertyVO ) );
        }
        if ( po.getPurchaseIntention() != null ) {
            customerBaseInsideRespVO.setPurchaseIntention( purchaseIntentionToString( String.valueOf( po.getPurchaseIntention() ), propertyVO ) );
        }
        if ( po.getAgeGroup() != null ) {
            customerBaseInsideRespVO.setAgeGroup( purchaseIntentionToString( String.valueOf( po.getAgeGroup() ), propertyVO ) );
        }
        if ( po.getGender() != null ) {
            customerBaseInsideRespVO.setGender( purchaseIntentionToString( String.valueOf( po.getGender() ), propertyVO ) );
        }
        customerBaseInsideRespVO.setId( po.getId() );
        customerBaseInsideRespVO.setName( po.getName() );
        customerBaseInsideRespVO.setMobile( po.getMobile() );
        customerBaseInsideRespVO.setWechatId( po.getWechatId() );

        return customerBaseInsideRespVO;
    }
}
