package com.qudian.idle.customer.center.server.facade.impl.app;

import com.qudian.idle.customer.center.api.facade.app.CustomerPropertyAppFacade;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.idle.customer.center.business.service.base.CustomerPropertyService;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR> Huang
 * @date 2025/8/13
 */
@Service(version = "1.0.0")
@Slf4j
@Api(tags = "配置属性管理")
@Validated
@RestController
public class CustomerPropertyAppFacadeImpl implements CustomerPropertyAppFacade {
    @Resource
    private CustomerPropertyService customerPropertyService;

    @Override
    @ApiOperation(value = "获取客户配置属性")
    @PostMapping("/base/properties")
    public BaseResponseDTO<CustomerPropertyRespVO> getPropertyList(@RequestBody BaseRequestVO requestVO) {
        CustomerPropertyRespVO customerConfigRespVO = customerPropertyService.getCustomerProperty();
        return ResponseBuilder.buildSuccess(Objects.isNull(customerConfigRespVO) ? new CustomerPropertyRespVO() : customerConfigRespVO);
    }

}
