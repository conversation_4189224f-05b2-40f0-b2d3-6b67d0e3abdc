package com.qudian.idle.customer.center.server.facade.impl.inside;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.customer.center.api.facade.inside.CustomerPropertyInsideFacade;
import com.qudian.idle.customer.center.api.vo.request.group.CustomerGroupListReqVO;
import com.qudian.idle.customer.center.api.vo.response.CustomerPropertyRespVO;
import com.qudian.idle.customer.center.api.vo.response.group.CustomerGroupListRespVO;
import com.qudian.idle.customer.center.infrastructure.converter.ContextUtil;
import com.qudian.idle.customer.center.business.service.base.CustomerGroupService;
import com.qudian.lme.base.builder.ResponseBuilder;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.request.BaseRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR> Huang
 * @date 2025/8/13
 */
@Service(version = "1.0.0")
@Slf4j
@Api(tags = "配置属性管理")
@Validated
@RestController
public class CustomerPropertyInsideFacadeImpl implements CustomerPropertyInsideFacade {
    @Value("${customer.search.properties:}")
    private String searchCustomerProperties;

    @Resource
    private CustomerGroupService customerGroupService;


    @Override
    @ApiOperation(value = "首页查询条件-获取客户配置属性")
    @PostMapping("/search/properties")
    public BaseResponseDTO<CustomerPropertyRespVO> getSearchPropertyList(@RequestBody BaseRequestVO requestVO) {
        log.info("op=start_CustomerConfigAppFacadeImpl.getSearchConfigList, requestVO={},customerProperties={}", requestVO,searchCustomerProperties);
        CustomerPropertyRespVO customerConfigRespVO = null;
        try {
            customerConfigRespVO = JSON.parseObject(searchCustomerProperties, CustomerPropertyRespVO.class);

            //设置组别：
            Long manageId = ContextUtil.getUserId(requestVO);
            if(Objects.nonNull(manageId)){
                CustomerGroupListReqVO groupListReqVO = CustomerGroupListReqVO.builder().build();
                groupListReqVO.setUserId(manageId);
                CustomerGroupListRespVO groupListRespVO = customerGroupService.list(groupListReqVO);
                customerConfigRespVO.getProperties().setCustomGroup(groupListRespVO.getCustomGroup());
            }

        } catch (Exception e) {
            log.error("[op=CustomerPropertyAppFacadeImpl.getConfigList error]错误信息:{}",e);
        }
        return ResponseBuilder.buildSuccess(Objects.isNull(customerConfigRespVO) ? new CustomerPropertyRespVO() : customerConfigRespVO);
    }


}
