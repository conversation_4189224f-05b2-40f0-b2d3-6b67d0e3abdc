package com.qudian.idle.customer.center.server;

import com.alibaba.fastjson.JSON;
import com.qudian.idle.customer.center.api.vo.share.OptionValues;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify OptionValues JSON deserialization works correctly with fastjson v1
 */
public class OptionValuesJsonTest {

    @Test
    public void testJsonDeserialization() {
        // Test JSON string that represents an array of OptionRecord objects
        String jsonString = "[{\"code\":1,\"title\":\"男\"}]";
        
        // Parse the JSON into OptionValues
        OptionValues optionValues = JSON.parseObject(jsonString, OptionValues.class);
        
        // Verify that the deserialization worked correctly
        assertNotNull(optionValues);
        assertEquals(1, optionValues.size());
        
        // Test requireSingleCode method - this should not throw an exception now
        String code = optionValues.requireSingleCode();
        assertEquals(String.valueOf(1), code);
        
        // Test code() method
        assertEquals(String.valueOf(1), optionValues.code());
        
        // Test title() method
        assertEquals("男", optionValues.title());
    }

    @Test
    public void testMultipleRecordsJsonDeserialization() {
        // Test JSON string with multiple records
        String jsonString = "[{\"code\":1,\"title\":\"男\"},{\"code\":2,\"title\":\"女\"}]";
        
        // Parse the JSON into OptionValues
        OptionValues optionValues = JSON.parseObject(jsonString, OptionValues.class);
        
        // Verify that the deserialization worked correctly
        assertNotNull(optionValues);
        assertEquals(2, optionValues.size());
        
        // Test that requireSingleCode throws exception for multiple records
        assertThrows(IllegalStateException.class, () -> optionValues.requireSingleCode());
        
        // Test codes() method
        assertEquals(2, optionValues.codes().size());
        assertTrue(optionValues.codes().contains(String.valueOf(1)));
        assertTrue(optionValues.codes().contains(String.valueOf(2)));
    }

    @Test
    public void testEmptyJsonDeserialization() {
        // Test empty JSON array
        String jsonString = "[]";
        
        // Parse the JSON into OptionValues
        OptionValues optionValues = JSON.parseObject(jsonString, OptionValues.class);
        
        // Verify that the deserialization worked correctly
        assertNotNull(optionValues);
        assertEquals(0, optionValues.size());
        
        // Test requireSingleCode method returns null for empty list
        assertNull(optionValues.requireSingleCode());
        
        // Test code() method returns null for empty list
        assertNull(optionValues.code());
        
        // Test title() method returns null for empty list
        assertNull(optionValues.title());
    }

    @Test
    public void testNullJsonDeserialization() {
        // Test null JSON
        String jsonString = "null";
        
        // Parse the JSON into OptionValues
        OptionValues optionValues = JSON.parseObject(jsonString, OptionValues.class);
        
        // Verify that null JSON results in null OptionValues
        assertNull(optionValues);
    }

    @Test
    public void testRealWorldScenario() {
        // Test the exact JSON structure from the error log
        String jsonString = "[{\"code\":1,\"title\":\"男\"}]";
        
        // Parse the JSON into OptionValues
        OptionValues optionValues = JSON.parseObject(jsonString, OptionValues.class);
        
        // This should work without throwing ClassCastException
        assertNotNull(optionValues);
        assertDoesNotThrow(() -> {
            String code = optionValues.requireSingleCode();
            assertEquals(String.valueOf(1), code);
        });
    }
}
