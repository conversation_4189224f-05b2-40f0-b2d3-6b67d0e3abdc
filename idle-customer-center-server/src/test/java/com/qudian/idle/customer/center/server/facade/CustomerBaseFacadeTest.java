package com.qudian.idle.customer.center.server.facade;

import com.github.javafaker.Faker;
import com.qudian.idle.customer.center.api.facade.app.CustomerBaseAppFacade;
import com.qudian.idle.customer.center.api.vo.share.ImageValues;
import com.qudian.idle.customer.center.api.vo.share.OptionValues;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerEditReqVO;
import com.qudian.idle.customer.center.api.vo.request.base.CustomerShowReqVO;
import com.qudian.idle.customer.center.api.vo.response.base.CustomerShowRespVO;
import com.qudian.idle.customer.center.server.TestStartApplication;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <p>文件名称:com.qudian.idle.customer.center.server.facade.CustomerBaseFacadeTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/14
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
public class CustomerBaseFacadeTest extends TestStartApplication {
    @Resource
    private CustomerBaseAppFacade customerBaseAppFacade;
    Faker faker = Faker.instance();

    @Test
    public void test01_addNewCustomer() {
        CustomerEditReqVO.CustomerEditBaseReqVO editBaseReqVO = new CustomerEditReqVO.CustomerEditBaseReqVO()
                .setName(faker.name().username())
                .setMobile(faker.phoneNumber().cellPhone())
                .setWechatId(faker.internet().emailAddress())
                .setGender(OptionValues.of("1", "男"))
                .setAgeGroup(OptionValues.of("1", "<24"))
                .setSource(OptionValues.of("1", "线上引流"))
                .setHomeVisitStatus(OptionValues.of("1", "接受上门"))
                .setContactMode(OptionValues.of("1", "电话"))
                .setCustomerType(OptionValues.of("1", "个人"))
                .setMembership(new CustomerEditReqVO.CustomerEditMembershipReqVO()
                        .setOutputIntention(OptionValues.of("1", "高意向"))
                        .setMembershipType(OptionValues.of("1", "普通"))
                        .setPurchaseIntention(OptionValues.of("1", "高意向"))
                )
                .setAddress(new CustomerEditReqVO.CustomerEditAddressReqVO()
                        .setProvince("北京")
                        .setCity("北京")
                        .setDistrict("朝阳区")
                        .setDetailAddress("朝阳区zh")
                )
                .setRemark(faker.lorem().sentence())
                .setImages(ImageValues.of(faker.internet().url(), faker.internet().url()));

        CustomerEditReqVO.CustomerEditExtReqVO editExtReqVO = new CustomerEditReqVO.CustomerEditExtReqVO()
                .setPreferType(OptionValues.of("1", "包袋"))
                .setBag(faker.commerce().department())
                .setJewel(faker.commerce().department())
                .setWatches(faker.commerce().department());

        CustomerEditReqVO addVO = new CustomerEditReqVO()
                .setBase(editBaseReqVO)
                .setPreference(editExtReqVO)
                .setManagerId(1L);
        customerBaseAppFacade.addOrEdit(addVO);
        assertThat(addVO).isNotNull();
    }

    @Test
    public void test02_queryCustomer() {
        CustomerShowReqVO reqVO = new CustomerShowReqVO(1L);
        BaseResponseDTO<CustomerShowRespVO> show = customerBaseAppFacade.show(reqVO);
        assertThat(show).isNotNull();
    }

    @Test
    public void test03_updateCustomer() {
        CustomerEditReqVO.CustomerEditBaseReqVO editBaseReqVO = new CustomerEditReqVO.CustomerEditBaseReqVO()
                .setId(1L)
                .setName(faker.name().username())
                .setMobile(faker.phoneNumber().cellPhone())
                .setWechatId(faker.internet().emailAddress())
                .setGender(OptionValues.of("2", "女"))
                .setAgeGroup(OptionValues.of("2", ">24"))
                .setSource(OptionValues.of("2", "线下引流"))
                .setHomeVisitStatus(OptionValues.of("1", "接受上门"))
                .setContactMode(OptionValues.of("1", "电话"))
                .setMembership(new CustomerEditReqVO.CustomerEditMembershipReqVO()
                        .setOutputIntention(OptionValues.of("1", "高意向"))
                        .setMembershipType(OptionValues.of("1", "普通"))
                        .setPurchaseIntention(OptionValues.of("1", "高意向"))
                )
                .setCustomerType(OptionValues.of("1", "个人"))
                .setAddress(new CustomerEditReqVO.CustomerEditAddressReqVO()
                        .setProvince("北京")
                        .setCity("北京")
                        .setDistrict("朝阳区")
                        .setDetailAddress("朝阳区zh")
                )
                .setRemark(faker.lorem().sentence())
                .setImages(ImageValues.of(faker.internet().url(), faker.internet().url()));

        CustomerEditReqVO.CustomerEditExtReqVO editExtReqVO = new CustomerEditReqVO.CustomerEditExtReqVO()
                .setPreferType(OptionValues.of("1", "包袋"))
                .setBag(faker.commerce().department())
                .setJewel(faker.commerce().department())
                .setWatches(faker.commerce().department());

        CustomerEditReqVO addVO = new CustomerEditReqVO()
                .setBase(editBaseReqVO)
                .setPreference(editExtReqVO)
                .setManagerId(1L);
        customerBaseAppFacade.addOrEdit(addVO);
    }
}
