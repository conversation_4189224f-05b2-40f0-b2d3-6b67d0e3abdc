package com.qudian.idle.customer.center.server.facade.inside;

import com.github.javafaker.Faker;
import com.qudian.idle.customer.center.api.facade.inside.CustomerBaseInsideFacade;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBaseInsideReqVO;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHomeReqVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerBaseInsideRespVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.server.TestStartApplication;
import com.qudian.lme.common.dto.BaseResponseDTO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.server.facade.CustomerBaseFacadeTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/14
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
public class CustomerInsideFacadeTest extends TestStartApplication {
    @Resource
    private CustomerBaseInsideFacade customerBaseInsideFacade;
    Faker faker = Faker.instance();

    @Test
    public void test01_batchQueryCustomerInfoInHome() {
        CustomerBatchQueryInHomeReqVO reqVO = new CustomerBatchQueryInHomeReqVO()
                .setIds(List.of(1L, 2L, 3L));
        BaseResponseDTO<List<CustomerInfoInHomeRespVO>> resp = customerBaseInsideFacade.batchQueryCustomerInfoInHome(reqVO);
    }

    @Test
    public void test02_queryCustomerBaseInfo() {
        CustomerBaseInsideReqVO reqVO = new CustomerBaseInsideReqVO().setCustomerId(1L);
        BaseResponseDTO<CustomerBaseInsideRespVO> resp = customerBaseInsideFacade.queryCustomerBaseInfo(reqVO);
    }

}
