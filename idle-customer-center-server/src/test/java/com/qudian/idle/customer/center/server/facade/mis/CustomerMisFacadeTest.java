package com.qudian.idle.customer.center.server.facade.mis;

import com.github.javafaker.Faker;
import com.qudian.idle.customer.center.api.facade.inside.CustomerBaseInsideFacade;
import com.qudian.idle.customer.center.api.facade.mis.CustomerBaseMisFacade;
import com.qudian.idle.customer.center.api.vo.request.inside.CustomerBatchQueryInHomeReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisPageReqVO;
import com.qudian.idle.customer.center.api.vo.request.mis.CustomerMisTransferReqVO;
import com.qudian.idle.customer.center.api.vo.response.inside.CustomerInfoInHomeRespVO;
import com.qudian.idle.customer.center.api.vo.response.mis.CustomerMisPageRespVO;
import com.qudian.idle.customer.center.server.TestStartApplication;
import com.qudian.lme.common.dto.BaseResponseDTO;
import com.qudian.pdt.api.toolkit.vo.response.BaseResponseVO;
import com.qudian.pdt.api.toolkit.vo.response.PagingList;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;

import java.util.List;

/**
 * <p>文件名称:com.qudian.idle.customer.center.server.facade.CustomerBaseFacadeTest</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/8/14
 */
@TestMethodOrder(MethodOrderer.MethodName.class)
public class CustomerMisFacadeTest extends TestStartApplication {
    @Resource
    private CustomerBaseMisFacade customerBaseMisFacade;
    Faker faker = Faker.instance();

    @Test
    public void test01_page() {
        CustomerMisPageReqVO reqVO = new CustomerMisPageReqVO();
        reqVO.setPageNum(1);
        reqVO.setPageSize(10);
        BaseResponseDTO<PagingList<CustomerMisPageRespVO>> resp = customerBaseMisFacade.page(reqVO);
    }

    @Test
    public void test02_transfer() {
        CustomerMisTransferReqVO reqVO = new CustomerMisTransferReqVO()
                .setCustomerId(1L)
                .setManagerName(faker.name().username())
                .setManagerId(2L);
        BaseResponseDTO<BaseResponseVO> resp = customerBaseMisFacade.transfer(reqVO);
    }


}
